/**
 * @fileoverview HTTP请求封装模块
 * @description 基于umi-request封装的HTTP请求工具，提供统一的错误处理、请求拦截和响应处理
 * <AUTHOR> Team
 */

import { extend, ResponseError, RequestOptionsInit } from 'umi-request';
import { YTHToast } from 'yth-ui';
import type { RequestMethod } from 'umi-request';
import { ConstHeaders } from './Constant';

/**
 * HTTP状态码对应的中文错误信息映射表
 * @description 用于将HTTP状态码转换为用户友好的中文错误提示
 */
const codeMessage: Record<number, string> = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  405: '请求方法不被允许。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

/**
 * 全局异常处理程序
 * @description 统一处理HTTP请求错误，包括网络错误和服务器错误
 * @param error - 响应错误对象
 * @returns 抛出处理后的错误对象
 * @see https://beta-pro.ant.design/docs/request-cn
 */
const errorHandler: (error: ResponseError) => ResponseError = (error: ResponseError) => {
  const { response } = error;

  // 处理有响应的错误（服务器返回了状态码）
  if (response && response.status) {
    const errorText: string = codeMessage[response.status] || response.statusText;
    const { status, url } = response;

    if (status && status === 401) {
      setTimeout(() => {
        window.location.href = `${window.location.origin}/user/login`;
      }, 2000);
    }

    // 显示错误提示Toast
    YTHToast.show({
      type: 'error',
      messageText: `请求错误 【${errorText}】${status}: ${url}`,
      p_props: { duration: 10 }, // PC端显示时长
      m_props: { duration: 3000 }, // 移动端显示时长
    });
  }

  // 处理网络错误（无响应）
  if (!response) {
    YTHToast.show({
      type: 'error',
      messageText: '您的网络发生异常，无法连接服务器',
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
  }

  // 重新抛出错误，供上层处理
  throw error;
};

/**
 * 默认的请求实例
 * @description 创建一个带有默认配置的请求实例，包含错误处理和cookie支持
 */
const requests: RequestMethod = extend({
  errorHandler, // 默认错误处理
  credentials: 'include', // 默认请求是否带上cookie
});

/**
 * 请求拦截器
 * @description 在每个请求发送前自动添加公共请求头
 */
requests.interceptors.request.use((url, options: RequestOptionsInit) => {
  const newOptions: RequestOptionsInit = {
    ...options,
    // 合并原有请求头和公共请求头
    headers: { ...options.headers, ...ConstHeaders() },
  };
  return { url, options: newOptions };
});

export default requests;

/**
 * 创建自定义请求实例
 * @description 根据指定的前缀和配置创建一个新的请求实例
 * @param prefix - API前缀路径，默认为'/gw/form-api'
 * @param options - 可选配置项
 * @param options.token - 认证令牌，会添加到Authorization头中
 * @param options.affinityHost - 亲和性主机，用于负载均衡
 * @returns 配置好的请求实例
 */
export const getRequest: (
  prefix?: string,
  { token, affinityHost }?: { token?: string; affinityHost?: string },
) => RequestMethod = (prefix = '/gw/form-api', { token = '', affinityHost = '' } = {}) => {
  const constHeaders: Headers = ConstHeaders();
  const headers: Record<string, string> = {};

  // 将Headers对象转换为普通对象，便于后续操作
  constHeaders.forEach((value, key) => {
    headers[key] = value;
  });

  // 添加认证令牌到请求头
  if (token) {
    headers.Authorization = token;
  }

  if (affinityHost) {
    headers.affinity_host = affinityHost;
  }

  // 创建请求实例
  const request: RequestMethod = extend({
    errorHandler, // 默认错误处理
    credentials: 'include', // 默认请求是否带上cookie
    timeout: 1000 * 20, // 默认请求超时时间（20秒）
    prefix, // API前缀
    headers, // 请求头
  });

  /**
   * 需要以Blob格式处理的Content-Type列表
   * @description 用于文件下载等场景的响应类型判断
   */
  const blobs: string[] = [
    'application/x-download',
    'application/vnd.ms-excel',
    'application/msword',
    'application/octet-stream',
  ];

  /**
   * 响应拦截器
   * @description 统一处理响应数据，包括文件下载和错误处理
   */
  request.interceptors.response.use(async (response, requestOptions) => {
    const contentType: string | null = response.headers.get('Content-Type');
    const isBlob: boolean = blobs.some((item) => contentType?.includes(item));

    // 处理文件下载类型的响应
    if (isBlob) {
      // eslint-disable-next-line no-param-reassign
      requestOptions.responseType = 'blob';
      return response;
    }

    // 处理非200状态码的响应
    if (response.status !== 200) {
      const errObj: { response: { status: number; statusText: string } } = {
        response: {
          status: response.status,
          // 获取对应的错误信息，如果没有则使用默认状态文本
          statusText:
            codeMessage[response.status as keyof typeof codeMessage] || response.statusText,
        },
      };
      throw errObj;
    }

    // 解析JSON响应体
    const { error, code, status, msg, data } = await response.clone().json();

    // 检查业务逻辑是否成功
    if ((code === 200 || code === 202) && !error) return response;

    // 构造业务错误对象
    const errObject: {
      response: { status: number; statusText: string; data: Record<string, unknown> };
    } = {
      response: {
        status: code || status,
        statusText: msg,
        data,
      },
    };
    throw errObject;
  });

  return request;
};

/**
 * 预配置的请求实例
 * @description 为不同的业务模块创建专用的请求实例
 */

/** 快开平台API请求实例 */
const formRequest: RequestMethod = getRequest('/gw/form-api');

/** 权限相关API请求实例 */
const rbacRequest: RequestMethod = getRequest('/gw/rbac-api/');

/** 安全基础模块API请求实例 */
const baseRequest: RequestMethod = getRequest('/gw/base-api');

/** 第三方监控管理API请求实例 */
const smpRequest: RequestMethod = getRequest('/gw/smp-api');

/** 教育培训API请求实例 */
const trainRequest: RequestMethod = getRequest('/gw/training-api/');

export { formRequest, baseRequest, rbacRequest, smpRequest, trainRequest };
