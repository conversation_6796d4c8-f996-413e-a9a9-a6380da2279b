{"name": "@yth/module-training", "version": "1.0.0", "description": "新版教育培训模块", "files": ["demo/", "es/", "lib/", "build/"], "main": "lib/index.js", "module": "es/index.js", "stylePath": "style.js", "scripts": {"start": "cross-env PLATFORM=dev build-scripts start", "build": "cross-env PLATFORM=prod build-scripts build", "test": "build-scripts test", "prepublishOnly": "npm run build", "lint": "npm run lint:eslint && npm run lint:stylelint", "lint:eslint": "eslint --ext .js,.jsx,.ts,.tsx src", "lint:stylelint": "stylelint \"**/*.{css,less}\" \"**/*.module.less\" ", "lint:fix": "npm run lint:eslint -- --fix && npm run lint:stylelint -- --fix", "prepare": "husky install"}, "keywords": ["test", "react", "component"], "dependencies": {"@ant-design/icons": "^4.8.0", "@react-spring/web": "9.6.0", "ahooks": "^3.8.4", "antd": "4.19.5", "classnames": "^2.5.1", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "moment": "^2.29.3", "prop-types": "^15.8.1", "regenerator-runtime": "^0.14.1", "template-component-demo": "^2.0.5", "umi-request": "^1.4.0", "yth-ui": "1.0.75"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@iceworks/spec": "^1.6.0", "@types/copy-webpack-plugin": "^10.1.3", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@typescript-eslint/project-service": "^8.33.1", "@yth/babel-plugin-import": "^1.13.12", "@yth/webpack-assets-hash-plugin": "^1.0.3", "build-plugin-component": "^1.12.2", "build-plugin-stark-module": "^2.0.0", "build-scripts": "^1.3.0", "compression-webpack-plugin": "^6.1.2", "copy-webpack-plugin": "^5.1.2", "enzyme": "^3.11.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "3.5.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "8.0.0", "lint-staged": "^16.1.0", "postcss-less": "^6.0.0", "prettier": "^3.5.3", "react": "^17.0.2", "react-dom": "^17.0.2", "stylelint": "^16.20.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-less": "^3.0.1", "terser-webpack-plugin": "^4", "webpack-bundle-analyzer": "^4.10.2", "worker-loader": "^3.0.8"}, "peerDependencies": {"react": "^16 || ^17"}, "componentConfig": {"name": "BusinessModuel", "title": "business-moduel", "category": "Business"}, "publishConfig": {"access": "public"}, "resolutions": {"@react-spring/web": "9.6.0"}, "overrides": {"@react-spring/web": "9.6.0"}, "license": "MIT", "homepage": "https://unpkg.com/@yth/test-moduel@1.0.0/build/index.html", "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": "eslint", "*.{css,less}": "stylelint --custom-syntax=postcss-less"}}